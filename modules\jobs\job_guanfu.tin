#NOP {官府任务模块 - 用于处理官府通缉犯任务的相关功能};
#NOP {模块功能说明:};
#NOP {1. 负责接取、执行和完成官府通缉犯任务};
#NOP {2. 支持自动判断通缉犯难度并选择合适目标};
#NOP {3. 包含通缉犯战斗、抓捕和交付的完整流程};
#NOP {4. 具有任务失败重试和放弃机制};

#NOP {全局变量说明:};
#NOP {guanfuafterdo: 官府任务完成后要执行的操作};
#VARIABLE {guanfuafterdo} {};
#NOP {guanfupendings: 存储未处理的通缉犯信息};
#VARIABLE {guanfupendings} {};

#NOP {jobgo_guanfu - 开始执行官府任务};
#NOP {参数说明: %1 - 任务完成后要执行的后续指令};
#ALIAS {jobgo_guanfu} {
  checkrequest {
    gotodo {扬州城} {广场} {
      startfull {jobask_guanfu {%1}};
    };
  };
};
#NOP {黑名单配置 - 存储不再尝试的通缉犯};
#VARIABLE {blacklist} {};

#NOP {jobask_guanfu - 接取通缉犯任务};
#NOP {功能说明:};
#NOP {1. 查看通缉榜并解析通缉犯信息};
#NOP {2. 根据经验值判断任务难度};
#NOP {3. 自动过滤黑名单中的通缉犯};
#NOP {4. 已失败的NPC会保存在guanfunpcs变量中，下次揭榜直接忽略};
#NOP {参数说明: %1 - 任务完成后的后续指令};
#ALIAS {jobask_guanfu} {
  #SEND {drop wenshu};
  #VARIABLE {joblocation} {};
  #VARIABLE {guanfupendings} {};
  #VARIABLE {wantedlist} {};
  #VARIABLE {reservelist} {};
  #VARIABLE {wenshuok} {0};
  #CLASS jobaskclass KILL;
  #CLASS jobaskclass OPEN;  
  #ACTION {^本府现通缉以下罪犯：} {   
    #ACTION {^    %*(%*)%!*，经验: %*，赏金: %*} {
      #VARIABLE {wantedlist[@lower{%%%2}]} {
        {name} {%%%1}
        {exp} {%%%3}        
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkwanted\"|你设定checkwanted为反馈信息}} {
    #NOP {反馈指令发起的时间戳};
    #VARIABLE {echots} {0};
    #NOP {设定任务难度，根据自己的exp判断};
    #LOCAL {selflevel} {@eval{$hp[exp] * $conf[guanfulv]}};     
    #NOP {清空reservelist，确保只存储符合条件的通缉犯};
    #VARIABLE {reservelist} {};
    #FOREACH {*wantedlist[]} {n} {
        #NOP {黑名单检查};
        #LOCAL {current_name} {@lower{$wantedlist[$n][name]}};
        #IF {@defined{blacklist[$current_name]} == 1} {
            #CONTINUE;
        };
        #NOP {榜单可能会持续很久,一小时开外的重新再接};
        #IF {"$guanfunpcs[$n]" != ""} {
            #IF {@elapsed{@eval{$guanfunpcs[$n]}} < 3600} {
                #CONTINUE;
            };
        };     
        #IF {$selflevel > $wantedlist[$n][exp]} {
            #VARIABLE {reservelist[$n]} {$wantedlist[$n][name]};
            #NOP {找到后设置标记};
            #VARIABLE {found_flag} {1};  
            #NOP {跳出循环};
            #BREAK;
        };           
    };
    echo {checkreserve}
  };
  #ACTION {^{设定环境变量：action \= \"checkreserve\"|你设定checkreserve为反馈信息}} {
    #VARIABLE {echots} {0};
    #LOCAL {jieid} {*reservelist[+1]};
    #IF {&reservelist[] > 0} {
      #VARIABLE {wenshuok} {0};      
      jie *reservelist[+1];
      set action checkjie*reservelist[+1];
      #VARIABLE {jobnpc_wanted} {$wantedlist[$jieid][name]};
      #VARIABLE {jobnpc_wanted_id} {$jieid};
      #CLASS jobaskclass KILL;      
      #NOP {按照距离对房间进行排序};
      jobfetch_guanfu;
    };
    #ELSE {
      joblog {没有找到适合的通缉犯。} {官府};
      doheal {jobcheck};      
    };
  };
  #ACTION {^目前治安良好，官府无任何通缉文书贴出。*} {        
    #CLASS jobaskclass KILL;
    #CLASS jobdoclass KILL;
    dohalt {      
        jobcheck;
    }
  };
  #ACTION {^你{分开行人来到近前，目光淡淡的扫视了几眼告示%*|走上前去看了看，“ 唰” 地一下把通缉%*的文书揭了下来，转身走出人群。|推开围观的行人，走到近前，微微睁开眼睛，冷冷瞥一眼看，随手揭了通缉%*|壮了壮胆，费力拔开行人，来到近前深深吸了口气，竭力镇定一下紧张%*}} {
    #VARIABLE {wenshuok} {1};
  };
  #ACTION {^你身上还揣着榜呢} {
    #VARIABLE {wenshuok} {2};
  }; 
  #ACTION {^设定环境变量：action \= \"checkjie%*\"} {
    #VARIABLE {echots} {0};
    dohalt {
      #IF {$wenshuok == 0} {
        #DELAY {1} {          
          jie %%1;
          echo {checkjie%%1};
        };
      };
      #ELSEIF {$wenshuok == 2} {
        #UNVARIABLE {reservelist[%%1]};
        drop wenshu;
        echo {checkreserve};
      };
      #ELSE {
        look wenshu;
        set action checkwenshu%%1;
      };
    }
  }; 
  #ACTION {^这是一张铁捕文书，可以拘捕} {
    #LINE ONESHOT #ACTION {^%!s此人%!*后，最后一次出现在%*附近。} {
      #VARIABLE {joblocation} {%%%1};
    };
    #LINE ONESHOT #ACTION {^%!s{就地格杀|缉拿归案}的赏金：} {
      #IF {"%%%1" == "就地格杀"} {
        #VARIABLE {jobnpc_wanted_kill} {1};
      };
      #ELSE {
        #VARIABLE {jobnpc_wanted_kill} {0};
      };
    };
  };
  #ACTION {^设定环境变量：action \= \"checkwenshu%*\"} {
    #VARIABLE {echots} {0};
    #IF {"$joblocation" == "黑木崖长廊"} {
      #VARIABLE {jobnpc_wanted_kill} {1};
    };
    #IF {"$common[guanfuplaces][$joblocation]" == "" || ("$joblocation" == "黑木崖长廊" && $jobnpc_wanted_kill == 0)} {
      #VARIABLE {guanfunpcs[%%1]} {@now{}};
    };
    #ELSE {
      #VARIABLE {guanfupendings[%%1]} {
        {name} {$reservelist[%%1]}
        {city} {$common[guanfuplaces][$joblocation][city]}
        {room} {$common[guanfuplaces][$joblocation][room]}
        {kill} {$jobnpc_wanted_kill}        
        {zone} {@getCityZone{$common[guanfuplaces][$joblocation][city]}}
      };
    };
    #VARIABLE {guanfuafterdo} {%1};
    #UNVARIABLE {reservelist[%%1]};    
    echo {checkreserve};
  }; 
  #CLASS jobaskclass CLOSE;
  #NOP {处理已超时的NPC};
  #LIST {overtimenpcs} {clear};
  #UNVARIABLE {guanfunpcs[]};
  #FOREACH {*guanfunpcs[]} {n} {
    #IF {@elapsed{$guanfunpcs[$n]} < 7200} {
      #CONTINUE;
    };
    #LIST {overtimenpcs} {add} {$n};
  };
  #FOREACH {$overtimenpcs[]} {n} {
    #UNVARIABLE {guanfunpcs[$n]};
  };  
  pfm_buff_normal;
  score;
  drop wenshu;
  l wanted list;
  set action checkwanted
};

#ALIAS {jobfetch_guanfu} {
  #IF {"$common[guanfuplaces][$joblocation]" == ""} {
    joblog {未能解析地址【$joblocation】，放弃任务。} {官府};
    jobfangqi_guanfu;
  };
  #ELSE {
    parsejoblocation {$joblocation} {jobdo_guanfu} {
      joblog {未能解析地址【$joblocation】。} {官府};
      jobfangqi_guanfu;
    } {5} {1} {1};
    excludeRooms;    
  };
};


#NOP {jobdo_guanfu - 执行通缉犯追捕任务};
#NOP {功能说明:};
#NOP {1. 记录任务开始时间};
#NOP {2. 设置相关触发器监控通缉犯};
#NOP {3. 在指定范围内搜索目标};
#NOP {参数说明: %1 - 当前是第几次搜索尝试};
#ALIAS {jobdo_guanfu} {  
  #VARIABLE {jobstart_ts} {@now{}};
  joblog {寻找位于【$joblocation】的通缉犯【$jobnpc_wanted】。} {官府};      
  #CLASS jobdoclass KILL; 
  #CLASS jobdoclass OPEN;
  #ACTION {$jobnpc_wanted(%*)} {
    #IF {"@lower{%%1}" == "$jobnpc_wanted_id"} {      
  #NOP    {joblog {发现【$jobnpc_wanted】。}; };
      #CLASS jobdoclass CLOSE;
      joblog {开始追捕【$jobnpc_wanted】。};
      #CLASS jobdoclass KILL;
      stopwalk;
      follow $jobnpc_wanted_id;
      #CLASS jobcheckclass KILL;
      jobfight_guanfu;
    };
  };
  #CLASS jobdoclass CLOSE;
  time;
  wwp;
  pfm_buff_normal;
  follow none;  
  jobnextroom {checkwanted {%1}};
};
#NOP {checkwanted - 检查当前房间是否有目标通缉犯};
#NOP {功能说明:};
#NOP {1. 检查目标是否在当前位置};
#NOP {2. 如果找到目标则开始战斗};
#NOP {3. 未找到则继续搜索下一个位置};

#ALIAS {checkwanted} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^这里没有 $jobnpc_wanted_id。} {   
    #CLASS jobcheckclass KILL;
    jobnextroom {checkwanted {%1}} {jobfail_guanfu {%1}};   
  };
  #ACTION {^你决定跟随$jobnpc_wanted一起行动。} {    
    #CLASS jobcheckclass KILL;
    jobfight_guanfu;
  };
  #CLASS jobcheckclass CLOSE;  
  follow $jobnpc_wanted_id;
};

#NOP {jobfail_guanfu - 处理通缉犯搜索失败的情况};
#NOP {功能说明:};
#NOP {1. 根据失败次数增加搜索半径};
#NOP {2. 超过最大尝试次数后将目标加入黑名单};
#NOP {3. 重新获取搜索范围内的房间列表};
#NOP {参数说明: %1 - 当前失败次数};
#ALIAS {jobfail_guanfu} {
  #LOCAL {iter} {@eval{@eval{%1} + 1}};
  #LOCAL {wanderroom} {$common[wanderwheres][$jobcity$jobroom]};
  #IF {$iter == 1} {
    joblog {未能找到位于【$joblocation】的【$jobnpc_wanted】，扩大范围搜索。};
    parsejoblocation {$joblocation} {jobdo_guanfu {$iter}} {jobfail_guanfu {$iter}} {7} {} {1};
    excludeRooms;
  };
  #ELSEIF {$iter == 2 && "$wanderroom" != ""} {
    joblog {还是未能找到位于【$joblocation】的【$jobnpc_wanted】，开始漫游搜索。};
    loc {gotoroom {$wanderroom[roomid]} {jobwander_guanfu {$wanderroom[roomname]} {$wanderroom[range]}}}
  };
  #ELSE {
    joblog {<aee>终究未能找到位于【$joblocation】的【$jobnpc_wanted】。};    
    #IF {"$jobnpc_wanted_id" != ""} {
      #VARIABLE {guanfunpcs[$jobnpc_wanted_id]} {@now{}};
      #UNVARIABLE {guanfupendings[$jobnpc_wanted_id]};
    };
    doheal {checkrequest {jobfangqi_guanfu}};
  };
};
#NOP {%1:房间名称,%2:步数};
#ALIAS {jobwander_guanfu} {
  job_wander {
    #DELAY {0.5} {
      #CLASS jobcheckclass KILL;
      #CLASS jobcheckclass OPEN;
      #ACTION {^这里没有 $jobnpc_wanted_id。} {
        #CLASS jobcheckclass KILL;
        jobwander_guanfu {%1} {@eval{%2 - 1}};
      };
      #ACTION {^你决定跟随$jobnpc_wanted一起行动。} {
        #CLASS jobcheckclass KILL;
        jobfight_guanfu;
      };
      #CLASS jobcheckclass CLOSE;
      follow $jobnpc_wanted_id;
    };
  } {jobfail_guanfu {3}} {%1} {%2}
};
#NOP {jobfight_guanfu - 处理与通缉犯的战斗};
#NOP {功能说明:};
#NOP {1. 记录战斗开始时间};
#NOP {2. 设置战斗相关触发器};
#NOP {3. 根据任务要求选择击杀或擒拿};
#NOP {4. 处理战斗结果和战利品};
#NOP {参数说明: %1 - 如果非空表示第二次战斗};
#ALIAS {jobfight_guanfu} {
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {jobnpc_wanted_desc} {};
  #VARIABLE {okflag} {0};
  #VARIABLE {overflag} {0};
  #VARIABLE {corpseindex} {1};
  #VARIABLE {corpsename} {};
  #VARIABLE {checkcount} {0};
  #VARIABLE {lastactionts} {0};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^$jobnpc_wanted{向后一纵，说道|脸色微变，说道|向后退了几步，说道|向后一纵，躬身做揖说道|哈哈大笑，说道：承让|双手一拱，笑着说道：承让|胜了这招，向后跃开三尺，笑道：承让}} {
    #IF {$jobnpc_wanted_kill == 0} {
      #IF {$overflag == 0} {
        #VARIABLE {overflag} {1};
        #DELAY {2} {
          #VARIABLE {overflag} {0};
          yun jingli;
          yun qi;
          hit $jobnpc_wanted_id;
        };
      };
    };
  };
  #ACTION {^你{向后一纵，说道|脸色微变，说道|向后退了几步，说道|向后一纵，躬身做揖说道|哈哈大笑，说道：承让|双手一拱，笑着说道：承让|胜了这招，向后跃开三尺，笑道：承让}} {
    #IF {$jobnpc_wanted_kill == 0} {
      #IF {$overflag == 0} {
        #VARIABLE {overflag} {1};
        #DELAY {2} {
          #VARIABLE {overflag} {0};
          yun jingli;
          yun qi;
          hit $jobnpc_wanted_id;
        };
      };
    };
  };
  #ACTION {^你正要有所动作，突然身旁有人将你一拍：好好看比武，别乱动} {
    #VARIABLE {idle} {0};
    #DELAY {2} {
      kill $jobnpc_wanted_id;
    };
  };
  #ACTION {^这里{禁止|不准}战斗} {
    #VARIABLE {idle} {0};
    #DELAY {2} {
      kill $jobnpc_wanted_id;
    };
  };
  #ACTION {^$jobnpc_wanted%*不拿出看家本领来是不行了} {
    #IF {$jobnpc_wanted_kill == 1} {
      kill $jobnpc_wanted_id;
    };
    #ELSE {
      hit $jobnpc_wanted_id;
    };
  };
  #ACTION {^$jobnpc_wanted%*今天吞下毒药来和你拼了！”} {
    #IF {$jobnpc_wanted_kill == 1} {
      kill $jobnpc_wanted_id;
    };
    #ELSE {
      hit $jobnpc_wanted_id;
    };
  };
  #ACTION {^你以本身修为判断$jobnpc_wanted的江湖历练大约是%*的级数} {
    #VARIABLE {jobnpc_wanted_desc} {%%1};
  };   
  #ACTION {^{设定环境变量：action \= \"checkcompare\"|你设定checkcompare为反馈信息}} {
    resonate {checkcompare};    
    startfight;
    #VARIABLE {lastactionts} {$checktimestamp};
    createpfm {@getFightPerform{}} {1} {$jobnpc_wanted_id};
    kill $jobnpc_wanted_id;    
  };  
  #ACTION {^$jobnpc_wanted突然缩成一团，滚在地上不停地发颤} {
    #NOP {怪蛇毒发不会死亡也不会变身，而且无法攻击，直接放弃};
    #CLASS jobfightclass KILL;
    stopfight;
    jobfangqi_guanfu
  };
  #ACTION {^$jobnpc_wanted神志迷糊，脚下一个不稳，倒在地上昏了过去} {
    #IF {$jobnpc_wanted_kill == 0} {
      closewimpy;
      dohalt {
        get $jobnpc_wanted_id;
        set action checkbody;
      } {0.2};
    };
    #ELSE {
      kill $jobnpc_wanted_id;
    };
  };
  #ACTION {^$jobnpc_wanted「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #VARIABLE {corpseindex} {1};
    #VARIABLE {corpsename} {};
    #VARIABLE {jobnpc_wanted_kill} {1};
    dohalt {
      get corpse $corpseindex;
      echo {checkcorpse};
    };
  };
  #ACTION {^你将$jobnpc_wanted扶了起来背在背上} {
    #VARIABLE {okflag} {1};
    #VARIABLE {corpseindex} {0};
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^你将%*的尸体扶了起来背在背上} {
    #VARIABLE {corpsename} {%%1};
  };
  #ACTION {^{设定环境变量：action \= \"checkcorpse\"|你设定checkcorpse为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {$corpseindex > 3} {
      #CLASS jobfightclass KILL;
      jobfangqi_guanfu
    };
    #ELSEIF {"$corpsename" == ""} {
      #MATH {corpseindex} {$corpseindex + 1};
      #DELAY {0.5} {
        get corpse $corpseindex;
        echo {checkcorpse};
      };
    };
    #ELSEIF {"$corpsename" != "$jobnpc_wanted"} {
      #MATH {corpseindex} {$corpseindex + 1};
        drop corpse;
        get corpse $corpseindex;
        echo {checkcorpse};
    };
    #ELSE {
      #CLASS jobfightclass KILL;
      #IF {"%1" == ""} {
        joblog {顺利搞定描述为【$jobnpc_wanted_desc】的通缉犯【$jobnpc_wanted】，耗时【@elapsed{$jobfight_ts}】秒。} {官府};
      };
      #ELSE {
        joblog {重新逮捕描述为【$jobnpc_wanted_desc】的通缉犯【$jobnpc_wanted】，耗时@elapsed{$jobfight_ts}秒。} {官府};
      };
      #DELAY {0.5} {
        dohalt {
          follow none;
          jobfinish_guanfu;
        };
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkbody\"|你设定checkbody为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$checkcount < 6} {
      #IF {$okflag == 0} {
        get $jobnpc_wanted_id;
      };
      #DELAY {0.5} {
        #IF {$corpseindex == 0} {
          echo {checkbody};
        };
      }
    };
    #ELSE {
      #CLASS jobfightclass KILL;
      dohalt {
        follow none;
        #IF {$okflag == 1} {
          joblog {顺利搞定描述为【$jobnpc_wanted_desc】的通缉犯【$jobnpc_wanted】，耗时【@elapsed{$jobfight_ts}】秒。} {官府};
          loc {
            jobfinish_guanfu;
          };
        };
        #ELSE {
          loc {
            jobfangqi_guanfu;
          };
        };
      }
    };
  };
  #CLASS jobfightclass CLOSE;
  
    wwp;
    pfm_buff_normal;
    jubu $jobnpc_wanted_id;
    compare $jobnpc_wanted_id;
    set action checkcompare;
};
#NOP {jobfinish_guanfu - 完成通缉犯任务};
#NOP {功能说明:};
#NOP {1. 带着通缉犯或尸体去见赵城之};
#NOP {2. 处理任务奖励};
#NOP {3. 清理相关变量和触发器};
#NOP {4. 执行后续指令};
#ALIAS {jobfinish_guanfu} {
  #CLASS jobdoclass KILL;
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^$jobnpc_wanted身子动了动，口中呻吟了几声，清醒过来} {
    stopwalk;
    dohalt {
      follow $jobnpc_wanted_id
    };
  };
  #ACTION {^这里没有 $jobnpc_wanted_id} {
    #CLASS jobfinishclass KILL;
    loc {
      doqudu {checkhp {jobfangqi_guanfu}};
    };
  };
  #ACTION {^你决定跟随$jobnpc_wanted一起行动} {
    #CLASS jobfinishclass KILL;
    jobfight_guanfu {1};
  };
  #CLASS jobfinishclass CLOSE;
  follow none;    
  gotonpc {赵城之} {jobfinish_guanfu_ask} 
};

#ALIAS {jobfinish_guanfu_ask} {
  #VARIABLE {okflag} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {givedo} {
    #IF {$jobnpc_wanted_kill == 1} {
      give corpse to zhao chengzhi;
    };
    #ELSE {
      give $jobnpc_wanted_id to zhao chengzhi;
    };
    echo {checkfinish};
  };
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^你身上没有这样东西} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^赵城之说道：「没看见本官正忙着吗？一边等着！」} {
    #VARIABLE {zhaobusy} {1};
  };
  #ACTION {^你得到了赏金%*，%*点潜能和%*点经验！} {    
    #VARIABLE {okflag} {1};
    #NOP {新增清空列表};
    #VARIABLE {guanfupendings} {};
    joblog {成功完成，获得【%%3】点经验【%%2】点潜能，耗时【@elapsed{$jobstart_ts}】秒。} {官府};
  };
  #ACTION {^{设定环境变量：action \= \"checkfinish\"|你设定checkfinish为反馈信息}} {
    #VARIABLE {echots} {0};
    #DELAY {2} {
      #IF {$okflag == 1} {
        #CLASS jobfinishclass KILL;
        #UNVARIABLE {guanfupendings[$jobnpc_wanted_id]};
        dohalt {
          drop wenshu;
          set;
          #IF {"$guanfuafterdo" == ""} {
            jobcheck;
          };
          #ELSE {
            doqudu {checkhp {checkrequest {$guanfuafterdo}}};
          };
        };
      };
      #ELSEIF {$okflag == 2} {
        #CLASS jobfinishclass KILL;
        dohalt {
          doqudu {checkhp {jobfangqi_guanfu}};
        };
      };
      #ELSEIF {$zhaobusy == 1} {
        #MATH {checkcount} {$checkcount + 1};
        #IF {$checkcount >= 3} {
          #CLASS jobfinishclass KILL;
          dohalt {
            #IF {$jobnpc_wanted_kill == 1} {
              drop corpse;
            };
            #ELSE {
              drop $jobnpc_wanted_id;
            };
            drop wenshu;
            #VARIABLE {env[wanted]} {0};
            killer_call {赵城之} {
              #IF {"$guanfuafterdo" == ""} {
                jobcheck;
              };
              #ELSE {
                doqudu {checkhp {checkrequest {$guanfuafterdo}}};
              };
            };
          };
        };
        #ELSE {
          #DELAY {2} {$givedo};
        };
      };
      #ELSE {
        $givedo;
      };
    };
  };
  #CLASS jobfinishclass CLOSE;
  $givedo;
};
#NOP {jobfangqi_guanfu - 放弃当前通缉犯任务};
#NOP {功能说明:};
#NOP {1. 清理任务相关变量和触发器};
#NOP {2. 记录放弃原因};
#NOP {3. 重置搜索半径};
#NOP {4. 准备接取新的任务};
#NOP {参数说明: %1 - 放弃任务的原因描述};
#ALIAS {jobfangqi_guanfu} {  
  #CLASS jobdoclass KILL;
  #NOP {新增清空列表};
  #VARIABLE {guanfupendings} {};       
  #IF {"$jobnpc_wanted_id" != ""} {
    #VARIABLE {guanfunpcs[$jobnpc_wanted_id]} {@now{}};
    #UNVARIABLE {guanfupendings[$jobnpc_wanted_id]};
  };
  drop wenshu;  
  #IF {"$guanfuafterdo" == ""} {
    doheal {jobcheck};
  };
  #ELSE {
    doheal {checkrequest {$guanfuafterdo}};
  };
};